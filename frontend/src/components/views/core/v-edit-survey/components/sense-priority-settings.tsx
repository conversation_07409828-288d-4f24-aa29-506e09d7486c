import { Component, Prop, State, h, Listen } from '@stencil/core';
import {
  UpdateByAttributeApi,
  GenerateUpdateByAttributePayload,
} from '../../../../../global/script/helpers';
import { Var, FrontendLogger } from '../../../../../global/script/var';

interface PriorityItem {
  value: string;
  label: string;
}

@Component({
  tag: 'sense-priority-settings',
  styleUrl: '../v-edit-survey.css',
  shadow: true,
})
export class SensePrioritySettings {
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() updatingField: string = '';

  // Question field status
  @State() questionMessage: string = '';
  @State() questionSuccess: boolean = false;

  // Max priorities field status
  @State() maxPrioritiesMessage: string = '';
  @State() maxPrioritiesSuccess: boolean = false;

  // Items field status
  @State() itemsMessage: string = '';
  @State() itemsSuccess: boolean = false;

  // Thank You Message field status
  @State() thankYouMessageMessage: string = '';
  @State() thankYouMessageSuccess: boolean = false;

  @State() newItem: string = '';
  @State() question: string = '';
  @State() maxPriorities: number = 3;
  @State() thankYouMessage: string = '';
  @State() isEditingQuestion: boolean = false;
  @State() isEditingMaxPriorities: boolean = false;
  @State() isEditingThankYou: boolean = false;

  componentWillLoad() {
    if (this.survey && this.survey.config) {
      this.question = this.survey.config.question || '';
      this.maxPriorities = this.survey.config.maxPriorities || 3;
      this.thankYouMessage = this.survey.config.thankYouMessage || 'Thank you for your response!';
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    if (event.detail.name === 'newItem') {
      this.newItem = event.detail.value;
      FrontendLogger.debug('New priority item input received', {
        itemValue: this.newItem,
        valueLength: this.newItem?.length || 0,
      });
    }
  }

  @Listen('listWithDeleteEvent')
  async handleListWithDeleteEvent(event: CustomEvent) {
    if (event.detail.name === 'deleteItem') {
      await this.removeItem(event.detail.value);
    }
  }

  @Listen('buttonClickEvent')
  async handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'addItem') {
      await this.addItem();
    }
  }

  @Listen('editableTextEvent')
  async handleEditableTextEvent(event: CustomEvent) {
    const { attribute, value } = event.detail;
    
    if (attribute === 'question') {
      await this.updateQuestion(value);
    } else if (attribute === 'maxPriorities') {
      await this.updateMaxPriorities(parseInt(value) || 3);
    } else if (attribute === 'thankYouMessage') {
      await this.updateThankYouMessage(value);
    }
  }

  // Update the question
  private async updateQuestion(newQuestion: string) {
    this.updatingField = 'question';

    if (!newQuestion || newQuestion.trim() === '') {
      this.questionMessage = 'Question cannot be empty';
      this.questionSuccess = false;
      this.updatingField = '';
      return;
    }

    const updatedConfig = { ...this.survey.config, question: newQuestion };
    await this.updateConfig(updatedConfig, { attribute: 'question', value: newQuestion });
  }

  // Update max priorities
  private async updateMaxPriorities(newMaxPriorities: number) {
    this.updatingField = 'maxPriorities';

    if (newMaxPriorities < 1 || newMaxPriorities > 10) {
      this.maxPrioritiesMessage = 'Max priorities must be between 1 and 10';
      this.maxPrioritiesSuccess = false;
      this.updatingField = '';
      return;
    }

    const updatedConfig = { ...this.survey.config, maxPriorities: newMaxPriorities };
    await this.updateConfig(updatedConfig, { attribute: 'maxPriorities', value: newMaxPriorities });
  }

  // Add a new priority item
  private async addItem() {
    if (!this.newItem || this.newItem.trim() === '') {
      this.itemsMessage = 'Priority item cannot be empty';
      this.itemsSuccess = false;
      return;
    }

    const currentItems = this.survey.config?.items || [];
    
    // Check for duplicates
    const isDuplicate = currentItems.some((item: PriorityItem) => 
      item.label.toLowerCase() === this.newItem.trim().toLowerCase()
    );

    if (isDuplicate) {
      this.itemsMessage = 'This priority item already exists';
      this.itemsSuccess = false;
      return;
    }

    const newItemObj: PriorityItem = {
      label: this.newItem.trim(),
      value: this.newItem.trim().toLowerCase().replace(/\s+/g, '-'),
    };

    const updatedItems = [...currentItems, newItemObj];
    const updatedConfig = { ...this.survey.config, items: updatedItems };

    this.newItem = '';
    await this.updateConfig(updatedConfig, { attribute: 'items', value: updatedItems });
  }

  // Remove a priority item
  private async removeItem(itemValue: string) {
    const currentItems = this.survey.config?.items || [];
    const updatedItems = currentItems.filter((item: PriorityItem) => item.value !== itemValue);
    
    const updatedConfig = { ...this.survey.config, items: updatedItems };
    await this.updateConfig(updatedConfig, { attribute: 'items', value: updatedItems });
  }

  // Update thank you message
  private async updateThankYouMessage(newMessage: string) {
    this.updatingField = 'thankYouMessage';
    const updatedConfig = { ...this.survey.config, thankYouMessage: newMessage };
    await this.updateConfig(updatedConfig, { attribute: 'thankYouMessage', value: newMessage });
  }

  // Helper method to update config
  private async updateConfig(updatedConfig: any, obj: any) {
    // Set field-specific editing states
    if (obj.attribute === 'question') {
      this.isEditingQuestion = true;
    } else if (obj.attribute === 'maxPriorities') {
      this.isEditingMaxPriorities = true;
    } else if (obj.attribute === 'thankYouMessage') {
      this.isEditingThankYou = true;
    }

    const updatePayload = GenerateUpdateByAttributePayload('config', updatedConfig);

    try {
      const { success, message } = await UpdateByAttributeApi(
        `${Var.api.endpoint.surveys}/${this.surveyId}`,
        updatePayload,
      );

      if (success) {
        // Update local survey object
        this.survey = { ...this.survey, config: updatedConfig };
        
        // Set success messages based on attribute
        if (obj.attribute === 'question') {
          this.questionMessage = 'Question updated successfully';
          this.questionSuccess = true;
          this.question = obj.value;
        } else if (obj.attribute === 'maxPriorities') {
          this.maxPrioritiesMessage = 'Max priorities updated successfully';
          this.maxPrioritiesSuccess = true;
          this.maxPriorities = obj.value;
        } else if (obj.attribute === 'items') {
          this.itemsMessage = 'Priority items updated successfully';
          this.itemsSuccess = true;
        } else if (obj.attribute === 'thankYouMessage') {
          this.thankYouMessageMessage = 'Thank you message updated successfully';
          this.thankYouMessageSuccess = true;
          this.thankYouMessage = obj.value;
        }
      } else {
        // Set error messages based on attribute
        if (obj.attribute === 'question') {
          this.questionMessage = message || 'Failed to update question';
          this.questionSuccess = false;
        } else if (obj.attribute === 'maxPriorities') {
          this.maxPrioritiesMessage = message || 'Failed to update max priorities';
          this.maxPrioritiesSuccess = false;
        } else if (obj.attribute === 'items') {
          this.itemsMessage = message || 'Failed to update priority items';
          this.itemsSuccess = false;
        } else if (obj.attribute === 'thankYouMessage') {
          this.thankYouMessageMessage = message || 'Failed to update thank you message';
          this.thankYouMessageSuccess = false;
        }
      }
    } catch (error) {
      FrontendLogger.error('Error updating survey config:', error);
      
      // Set generic error messages
      if (obj.attribute === 'question') {
        this.questionMessage = 'An error occurred while updating the question';
        this.questionSuccess = false;
      } else if (obj.attribute === 'maxPriorities') {
        this.maxPrioritiesMessage = 'An error occurred while updating max priorities';
        this.maxPrioritiesSuccess = false;
      } else if (obj.attribute === 'items') {
        this.itemsMessage = 'An error occurred while updating priority items';
        this.itemsSuccess = false;
      } else if (obj.attribute === 'thankYouMessage') {
        this.thankYouMessageMessage = 'An error occurred while updating thank you message';
        this.thankYouMessageSuccess = false;
      }
    } finally {
      // Reset editing states and updating field
      this.isEditingQuestion = false;
      this.isEditingMaxPriorities = false;
      this.isEditingThankYou = false;
      this.updatingField = '';

      // Clear messages after 4 seconds
      setTimeout(() => {
        this.questionMessage = '';
        this.maxPrioritiesMessage = '';
        this.itemsMessage = '';
        this.thankYouMessageMessage = '';
      }, 4000);
    }
  }

  private formatItemsForList(): string {
    if (!this.survey?.config?.items?.length) return '[]';

    const items = this.survey.config.items as PriorityItem[];
    return JSON.stringify(items);
  }

  render() {
    return (
      <div class="settings-section">
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>1. What priority question would you like to ask?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Question"
          type="text"
          value={this.question}
          entity="survey"
          attribute="question"
          bypass={true}
          active={this.isEditingQuestion}
        ></e-text-editable>

        {this.questionMessage && this.updatingField !== 'question' && (
          <p-notification
            message={this.questionMessage}
            theme={this.questionSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>2. Maximum number of priorities users can select</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Max Priorities"
          type="number"
          value={this.maxPriorities.toString()}
          entity="survey"
          attribute="maxPriorities"
          bypass={true}
          active={this.isEditingMaxPriorities}
        ></e-text-editable>

        {this.maxPrioritiesMessage && this.updatingField !== 'maxPriorities' && (
          <p-notification
            message={this.maxPrioritiesMessage}
            theme={this.maxPrioritiesSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>3. What priority items would you like users to choose from?</strong>
        </e-text>
        <l-spacer value={1}></l-spacer>
        <l-row justifyContent="flex-start">
          <e-input
            type="text"
            name="newItem"
            placeholder="e.g. Dark mode, Mobile app, Better search"
            value={this.newItem}
          ></e-input>
          <l-spacer variant="horizontal" value={0.5}></l-spacer>
          <e-button
            variant="ghost"
            disabled={!this.newItem}
            onClick={() => this.addItem()}
            action="addItem"
          >
            Add
          </e-button>
        </l-row>
        <l-spacer value={1}></l-spacer>
        <p-list-with-delete
          name="deleteItem"
          items={this.formatItemsForList()}
          emptyMessage="No priority items added yet"
        ></p-list-with-delete>

        {this.itemsMessage && this.updatingField !== 'items' && (
          <p-notification
            message={this.itemsMessage}
            theme={this.itemsSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>4. Thank You Message</strong>
        </e-text>
        <e-text variant="footnote">
          This message will be shown to respondents after they submit their response
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Thank You Message"
          type="text"
          value={this.thankYouMessage}
          entity="survey"
          attribute="thankYouMessage"
          bypass={true}
          active={this.isEditingThankYou}
        ></e-text-editable>
        {this.thankYouMessageMessage && this.updatingField !== 'thankYouMessage' && (
          <p-notification
            message={this.thankYouMessageMessage}
            theme={this.thankYouMessageSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}
      </div>
    );
  }
}
