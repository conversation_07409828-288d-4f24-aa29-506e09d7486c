import { sensePriorityConfigInterface, PriorityItem } from '../../interfaces';

export const generateSensePriorityConfig = (
  question: string,
  items: PriorityItem[],
  maxPriorities: number,
  thankYouMessage: string,
) => {
  let payload: sensePriorityConfigInterface = {
    question: question,
    items: items,
    maxPriorities: maxPriorities,
    thankYouMessage: thankYouMessage || 'Thank you for your response!',
  };

  return payload;
};
